#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建缴费明细导出模板Excel文件
"""

import os

def create_export_template():
    """创建缴费明细导出模板"""

    # 创建CSV格式的模板文件，然后手动转换为xls
    # 由于Python环境问题，我们先创建一个简单的文本模板

    template_content = """日期\t业务代码_商户号\t子业务代码\t业务名称\t商户号\t业务代码\t应付笔数\t应付金额(元)\t应收笔数\t应收金额(元)\t轧差金额(元)
{{fe:txnLogDetailList t.setdate}}\t{{t.str}}\t{{t.str30}}\t{{t.str31}}\t{{t.merchid}}\t{{t.opecd}}\t{{t.num}}\t{{t.amt/100}}\t{{t.receivableNum}}\t{{t.receivableAmt/100}}\t{{t.netAmt}}"""

    # 保存文件
    output_path = 'xm-xzp-impl/src/main/resources/doc/xzp/缴费明细导出模板.txt'

    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # 保存模板内容
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(template_content)

    print(f"模板文件已创建: {output_path}")
    print("请手动将此文件转换为Excel格式(.xls)并重命名为'缴费明细导出模板.xls'")

if __name__ == '__main__':
    create_export_template()
