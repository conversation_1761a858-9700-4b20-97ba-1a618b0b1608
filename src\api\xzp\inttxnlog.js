import request from '@/utils/request'

// 查询交易流水列表
export function listIntTxnLog(query) {
  return request({
    url: '/api/xzp/intTxnLog/intTxnLogList',
    method: 'post',
    data: query
  })
}

// 查询交易流水列表（关联查询）
export function selectIntTxnLogList(query) {
  return request({
    url: '/api/xzp/intTxnLog/selectIntTxnLogList',
    method: 'post',
    data: query
  })
}

// 查询缴费明细
export function queryTxnLogDetail(query, pageNum = 1, pageSize = 20) {
  return request({
    url: `/api/admin/xzp/txn/queryTxnLogDetail?pageNum=${pageNum}&pageSize=${pageSize}`,
    method: 'post',
    data: query
  })
}

// 导出缴费明细
export function exportTxnLogDetail(query) {
  return request({
    url: '/api/admin/xzp/txn/exportTxnLogDetail',
    method: 'post',
    data: query,
    responseType: 'blob',
    timeout: 60000
  })
}