<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" class="search-form" label-width="100px" size="small">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="缴费号码">
              <el-input
                v-model="search.account"
                placeholder="请输入缴费号码"
                style="width: 200px;"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户号">
              <el-input
                v-model="search.payId"
                placeholder="请输入用户号"
                style="width: 200px;"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="委托单位代码">
              <el-input
                v-model="search.merchid"
                placeholder="请输入委托单位代码"
                style="width: 200px;"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务代码">
              <el-input
                v-model="search.opecd"
                placeholder="请输入业务代码"
                style="width: 200px;"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="子业务号">
              <el-input
                v-model="search.subOpecd"
                placeholder="请输入子业务号"
                style="width: 200px;"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="search-btns">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery" :loading="queryLoading">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 查询结果展示区域 -->
    <el-card v-if="queryResult" class="result-card">
      <div slot="header" class="clearfix">
        <span>查询结果</span>
      </div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="响应码">{{ queryResult.code }}</el-descriptions-item>
        <el-descriptions-item label="响应消息">{{ queryResult.message }}</el-descriptions-item>
        <el-descriptions-item label="查询时间">{{ queryTime }}</el-descriptions-item>
        <el-descriptions-item label="缴费号码">{{ search.account }}</el-descriptions-item>
      </el-descriptions>
      
      <div style="margin-top: 20px;">
        <h4>响应数据：</h4>
        <el-input
          type="textarea"
          :rows="10"
          v-model="formattedResult"
          readonly
          style="font-family: monospace;"
        ></el-input>
      </div>
    </el-card>
  </div>
</template>

<script>
import { depositNoticeQuery } from '@/api/depositnotice';

export default {
  name: 'DepositNotice',
  dicts: ['xzp_wl_url'],
  data() {
    return {
      search: {
        account: '',
        payId: '',
        merchid: '',
        opecd: '',
        subOpecd: ''
      },
      queryLoading: false,
      queryResult: null,
      queryTime: '',
      formattedResult: ''
    };
  },
  methods: {
    // 提交查询
    async handleQuery() {
      if (!this.search.account) {
        this.$message.warning('请输入缴费号码');
        return;
      }

      this.queryLoading = true;
      try {
        // 从数据字典获取目标URL (读取w0002的值)
        const targetUrl = this.dict.xzp_wl_url && this.dict.xzp_wl_url.length > 0 
          ? this.dict.xzp_wl_url.find(item => item.dictLabel === 'w0002')?.dictValue 
          : null;
        
        const requestData = {
          busikind: 'DEPOSIT',
          tradecode: 'Card',
          merchid: this.search.merchid || '************',
          opecd: this.search.opecd || '101064',
          sub_ope_cd: this.search.subOpecd,
          empname: this.$store.state.user.name || this.$store.state.user.userName,
          empcode: this.$store.state.user.empCode || this.$store.state.user.userId, 
          orgcode: this.$store.state.user.orgCode || this.$store.state.user.instId, 
          orgdegree: this.$store.state.user.orgDegree || this.$store.state.user.instLvl || '2',
          account: this.search.account,
          pay_id: this.search.payId,
          targetUrl: targetUrl,
          action: 'deposit_notice'
        };

        const response = await depositNoticeQuery(requestData);
        this.queryResult = response;
        this.queryTime = new Date().toLocaleString();
        this.formattedResult = JSON.stringify(response, null, 2);
        
        this.$message.success('查询成功');
      } catch (error) {
        console.error('Query error:', error);
        this.$message.error('查询失败：' + (error.message || '未知错误'));
      } finally {
        this.queryLoading = false;
      }
    },

    // 重置查询
    resetQuery() {
      this.search = {
        account: '',
        payId: '',
        merchid: '',
        opecd: '',
        subOpecd: ''
      };
      this.queryResult = null;
      this.queryTime = '';
      this.formattedResult = '';
    }
  }
};
</script>

<style scoped>
.search-form {
  margin-bottom: 10px;
}

.app-container {
  padding: 20px;
}

.search-btns {
  text-align: right;
}

.result-card {
  margin-top: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>