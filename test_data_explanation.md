# 缴费明细查询测试数据说明

## 📊 测试数据设计

### 🏢 商户配置
- **350200050235**: 支持水费(198020)、电费(101001)、燃气费(101002)
- **350200050238**: 支持水费(198020)、电费(101001)

### 💧 水费业务 (ope_cd = '198020')
**子业务类型：**
- `000001`: 居民用水
- `000002`: 商业用水  
- `000003`: 工业用水

**pay_id格式：** 子业务代码(6位) + 用户编号(4位)
- 例如：`0000011001` = 子业务`000001` + 用户`1001`

### ⚡ 电费业务 (ope_cd = '101001')
- 无子业务分类
- pay_id直接使用用户编号

## 📅 测试数据分布（重点测试同一天同一业务代码的分组小计）

### 2025年7月1日 - 重点测试日
**350200050235 + 198020 (水费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| 000001 | 居民用水 | 2 | 12,500 | 125.00 |
| 000002 | 商业用水 | 3 | 26,500 | 265.00 |
| 000003 | 工业用水 | 2 | 27,000 | 270.00 |
| **小计** | **水费小计** | **7** | **66,000** | **660.00** |

**350200050235 + 101001 (电费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| - | 电费缴费 | 3 | 19,500 | 195.00 |
| **小计** | **电费小计** | **3** | **19,500** | **195.00** |

**350200050238 + 198020 (水费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| 000001 | 居民用水 | 2 | 11,000 | 110.00 |
| 000002 | 商业用水 | 2 | 19,000 | 190.00 |
| **小计** | **水费小计** | **4** | **30,000** | **300.00** |

### 2025年7月2日
**350200050235 + 198020 (水费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| 000001 | 居民用水 | 2 | 14,000 | 140.00 |
| 000002 | 商业用水 | 1 | 8,500 | 85.00 |
| 000003 | 工业用水 | 1 | 20,000 | 200.00 |
| **小计** | **水费小计** | **4** | **42,500** | **425.00** |

**350200050235 + 101001 (电费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| - | 电费缴费 | 2 | 13,000 | 130.00 |
| **小计** | **电费小计** | **2** | **13,000** | **130.00** |

**350200050238 + 198020 (水费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| 000001 | 居民用水 | 1 | 5,500 | 55.00 |
| 000002 | 商业用水 | 1 | 9,500 | 95.00 |
| **小计** | **水费小计** | **2** | **15,000** | **150.00** |

### 2025年7月3日
**350200050235 + 198020 (水费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| 000001 | 居民用水 | 1 | 5,000 | 50.00 |
| 000003 | 工业用水 | 1 | 18,000 | 180.00 |
| **小计** | **水费小计** | **2** | **23,000** | **230.00** |

**350200050235 + 101001 (电费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| - | 电费缴费 | 1 | 8,000 | 80.00 |
| **小计** | **电费小计** | **1** | **8,000** | **80.00** |

**350200050238 + 198020 (水费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| 000001 | 居民用水 | 1 | 4,800 | 48.00 |
| **小计** | **水费小计** | **1** | **4,800** | **48.00** |

### 2025年7月4日
**350200050235 + 198020 (水费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| 000001 | 居民用水 | 1 | 5,500 | 55.00 |
| **小计** | **水费小计** | **1** | **5,500** | **55.00** |

**350200050235 + 101001 (电费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| - | 电费缴费 | 1 | 6,500 | 65.00 |
| **小计** | **电费小计** | **1** | **6,500** | **65.00** |

**350200050238 + 198020 (水费)：**
| 子业务代码 | 子业务名称 | 笔数 | 金额(分) | 金额(元) |
|------------|------------|------|----------|----------|
| 000001 | 居民用水 | 1 | 5,200 | 52.00 |
| **小计** | **水费小计** | **1** | **5,200** | **52.00** |

## 🎯 预期的分组小计效果

查询日期范围：2025-07-01 到 2025-07-04

**每日每个业务组合应该显示：**
1. 具体的子业务明细行（按子业务代码分组）
2. 该业务组合的小计行（在业务代码列显示"小计"）

**例如 2025-07-01 的 350200050235 水费业务（重点测试）：**
```
20250701  198020_350200050235  350200050235  000001  居民用水  2笔  125.00元
20250701  198020_350200050235  350200050235  000002  商业用水  3笔  265.00元
20250701  198020_350200050235  350200050235  000003  工业用水  2笔  270.00元
20250701  小计                                                  7笔  660.00元
```

**关键测试点：**
- ✅ 同一天同一业务代码有多个子业务明细
- ✅ 每个子业务显示正确的笔数和金额汇总
- ✅ 小计行显示该业务组合的总计
- ✅ 小计行在业务代码列显示"小计"，其他分类字段为空

## 🧪 测试步骤

1. **执行SQL插入脚本**
2. **访问缴费明细查询页面**
3. **设置查询条件：**
   - 起始日期：20250701
   - 终止日期：20250704
4. **点击查询**
5. **验证结果：**
   - 数据按日期、业务分组
   - 每组最后有小计行
   - 小计行样式突出
   - 子业务代码正确显示

## 🔍 关键验证点

- [ ] 子业务代码列显示正确（000001、000002、000003）
- [ ] 小计行在每组最后
- [ ] 小计行业务代码列显示"小计"
- [ ] 小计行其他分类字段为空
- [ ] 金额和笔数汇总正确
- [ ] 样式效果符合预期
