-- 缴费明细查询测试数据插入脚本
-- 用于测试分组小计功能

-- =====================================================
-- 1. 清理测试数据（可选）
-- =====================================================
DELETE FROM tb_int_txn_log_temp WHERE merch_id IN ('350200050235', '350200050238');
DELETE FROM tb_merch_sub_ope WHERE merch_id IN ('350200050235', '350200050238');
DELETE FROM tb_merch_ope WHERE merch_id IN ('350200050235', '350200050238');

-- =====================================================
-- 2. 插入商户操作表数据 (tb_merch_ope)
-- =====================================================
INSERT INTO tb_merch_ope (merch_id, ope_cd, prdt_nm) VALUES
('350200050235', '198020', '水费缴费'),
('350200050235', '101001', '电费缴费'),
('350200050235', '101002', '燃气缴费'),
('350200050238', '198020', '水费缴费'),
('350200050238', '101001', '电费缴费');

-- =====================================================
-- 3. 插入商户子操作表数据 (tb_merch_sub_ope)
-- =====================================================
INSERT INTO tb_merch_sub_ope (merch_id, ope_cd, sub_ope_id, sub_ope_nm) VALUES
-- 350200050235 的子业务
('350200050235', '198020', '000001', '居民用水'),
('350200050235', '198020', '000002', '商业用水'),
('350200050235', '198020', '000003', '工业用水'),
('350200050238', '198020', '000001', '居民用水'),
('350200050238', '198020', '000002', '商业用水');

-- =====================================================
-- 4. 插入交易流水测试数据 (tb_int_txn_log_temp)
-- =====================================================

-- 2025年7月1日数据 - 重点测试同一天同一业务代码的分组小计
INSERT INTO tb_int_txn_log_temp (
    tran_dt, local_dt, unite_clr_dt, merch_id, ope_cd, pay_id,
    tran_at, tran_stat_cd, tran_cd, sub_ope_cd
) VALUES
-- 350200050235 + 198020 (水费) - 同一天同一业务代码，多个子业务
('20250701', '20250701', '20250701', '350200050235', '198020', '0000011001', 5000, '011', 'TXN001', '000001'),
('20250701', '20250701', '20250701', '350200050235', '198020', '0000011002', 7500, '011', 'TXN002', '000001'),
('20250701', '20250701', '20250701', '350200050235', '198020', '0000021001', 12000, '011', 'TXN003', '000002'),
('20250701', '20250701', '20250701', '350200050235', '198020', '0000021002', 8500, '011', 'TXN004', '000002'),
('20250701', '20250701', '20250701', '350200050235', '198020', '0000021003', 6000, '011', 'TXN005', '000002'),
('20250701', '20250701', '20250701', '350200050235', '198020', '0000031001', 15000, '011', 'TXN006', '000003'),
('20250701', '20250701', '20250701', '350200050235', '198020', '0000031002', 12000, '011', 'TXN007', '000003'),
-- 预期小计：居民用水2笔125元，商业用水3笔265元，工业用水2笔270元，总计7笔660元

-- 350200050235 + 101001 (电费) - 同一天同一业务代码，无子业务分类
('20250701', '20250701', '20250701', '350200050235', '101001', 'ELE001', 6000, '011', 'TXN008', ''),
('20250701', '20250701', '20250701', '350200050235', '101001', 'ELE002', 9000, '011', 'TXN009', ''),
('20250701', '20250701', '20250701', '350200050235', '101001', 'ELE003', 4500, '011', 'TXN010', ''),
-- 预期小计：电费缴费3笔195元

-- 350200050238 + 198020 (水费) - 同一天同一业务代码，多个子业务
('20250701', '20250701', '20250701', '350200050238', '198020', '0000011003', 4500, '011', 'TXN011', '000001'),
('20250701', '20250701', '20250701', '350200050238', '198020', '0000011004', 6500, '011', 'TXN012', '000001'),
('20250701', '20250701', '20250701', '350200050238', '198020', '0000021003', 11000, '011', 'TXN013', '000002'),
('20250701', '20250701', '20250701', '350200050238', '198020', '0000021004', 8000, '011', 'TXN014', '000002'),
-- 预期小计：居民用水2笔110元，商业用水2笔190元，总计4笔300元

-- 2025年7月2日数据 - 测试跨日期的分组
('20250702', '20250702', '20250702', '350200050235', '198020', '0000011005', 6000, '011', 'TXN015', '000001'),
('20250702', '20250702', '20250702', '350200050235', '198020', '0000011006', 8000, '011', 'TXN016', '000001'),
('20250702', '20250702', '20250702', '350200050235', '198020', '0000021005', 8500, '011', 'TXN017', '000002'),
('20250702', '20250702', '20250702', '350200050235', '198020', '0000031003', 20000, '011', 'TXN018', '000003'),
-- 预期小计：居民用水2笔140元，商业用水1笔85元，工业用水1笔200元，总计4笔425元

('20250702', '20250702', '20250702', '350200050235', '101001', 'ELE004', 7500, '011', 'TXN019', ''),
('20250702', '20250702', '20250702', '350200050235', '101001', 'ELE005', 5500, '011', 'TXN020', ''),
-- 预期小计：电费缴费2笔130元

('20250702', '20250702', '20250702', '350200050238', '198020', '0000011007', 5500, '011', 'TXN021', '000001'),
('20250702', '20250702', '20250702', '350200050238', '198020', '0000021006', 9500, '011', 'TXN022', '000002'),
-- 预期小计：居民用水1笔55元，商业用水1笔95元，总计2笔150元

-- 2025年7月3日数据
('20250703', '20250703', '20250703', '350200050235', '198020', '0000011008', 5000, '011', 'TXN023', '000001'),
('20250703', '20250703', '20250703', '350200050235', '198020', '0000031004', 18000, '011', 'TXN024', '000003'),
-- 预期小计：居民用水1笔50元，工业用水1笔180元，总计2笔230元

('20250703', '20250703', '20250703', '350200050235', '101001', 'ELE006', 8000, '011', 'TXN025', ''),
-- 预期小计：电费缴费1笔80元

('20250703', '20250703', '20250703', '350200050238', '198020', '0000011009', 4800, '011', 'TXN026', '000001'),
-- 预期小计：居民用水1笔48元

-- 2025年7月4日数据
('20250704', '20250704', '20250704', '350200050235', '198020', '0000011010', 5500, '011', 'TXN027', '000001'),
-- 预期小计：居民用水1笔55元

('20250704', '20250704', '20250704', '350200050235', '101001', 'ELE007', 6500, '011', 'TXN028', ''),
-- 预期小计：电费缴费1笔65元

('20250704', '20250704', '20250704', '350200050238', '198020', '0000011011', 5200, '011', 'TXN029', '000001');
-- 预期小计：居民用水1笔52元

-- =====================================================
-- 5. 验证数据插入
-- =====================================================
-- 查看插入的数据
SELECT '商户操作表数据' as table_name;
SELECT * FROM tb_merch_ope WHERE merch_id IN ('350200050235', '350200050238');

SELECT '商户子操作表数据' as table_name;
SELECT * FROM tb_merch_sub_ope WHERE merch_id IN ('350200050235', '350200050238');

SELECT '交易流水数据统计' as table_name;
SELECT 
    local_dt,
    merch_id,
    ope_cd,
    COUNT(*) as record_count,
    SUM(tran_at) as total_amount
FROM tb_int_txn_log_temp 
WHERE merch_id IN ('350200050235', '350200050238')
GROUP BY local_dt, merch_id, ope_cd
ORDER BY local_dt, merch_id, ope_cd;
