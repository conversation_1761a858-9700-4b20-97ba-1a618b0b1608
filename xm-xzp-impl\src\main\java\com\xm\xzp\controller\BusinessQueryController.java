package com.xm.xzp.controller;

import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.BusinessQueryApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.vo.BusinessQueryVo;
import com.xm.xzp.model.vo.CardChangeNoticeVo;
import com.xm.xzp.model.vo.DepositNoticeVo;
import com.xm.xzp.service.IBusinessQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 业务查询控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@Component
public class BusinessQueryController implements BusinessQueryApi {

    @Resource
    private IBusinessQueryService businessQueryService;

    @Override
    @PMCTLLog(name = "业务查询转发", action = "转发")
    public RestResponse<Object> businessQuery(BusinessQueryVo businessQueryVo) {
        log.info("接收到业务查询请求，账户: {}, 目标URL: {}", businessQueryVo.getAccount(), businessQueryVo.getTargetUrl());
        
        try {
            // 参数验证
            if (businessQueryVo.getTargetUrl() == null || businessQueryVo.getTargetUrl().trim().isEmpty()) {
                log.warn("目标URL为空，无法执行转发");
                return RestResponse.fail("目标URL不能为空");
            }
            
            // 执行业务查询转发
            Object result = businessQueryService.executeBusinessQuery(businessQueryVo);
            
            log.info("业务查询转发完成，账户: {}", businessQueryVo.getAccount());
            return RestResponse.success(result);
            
        } catch (Exception e) {
            log.error("业务查询转发异常，账户: {}, 错误信息: {}", businessQueryVo.getAccount(), e.getMessage(), e);
            return RestResponse.fail("业务查询转发失败: " + e.getMessage());
        }
    }

    @Override
    @PMCTLLog(name = "换卡通知转发", action = "转发")
    public RestResponse<Object> cardChangeNotice(CardChangeNoticeVo cardChangeNoticeVo) {
        log.info("接收到换卡通知请求，用户号: {}, 目标URL: {}", cardChangeNoticeVo.getPayid(), cardChangeNoticeVo.getTargetUrl());
        
        try {
            // 参数验证
            if (cardChangeNoticeVo.getTargetUrl() == null || cardChangeNoticeVo.getTargetUrl().trim().isEmpty()) {
                log.warn("目标URL为空，无法执行转发");
                return RestResponse.fail("目标URL不能为空");
            }
            
            // 执行换卡通知转发
            Object result = businessQueryService.executeCardChangeNotice(cardChangeNoticeVo);
            
            log.info("换卡通知转发完成，用户号: {}", cardChangeNoticeVo.getPayid());
            return RestResponse.success(result);
            
        } catch (Exception e) {
            log.error("换卡通知转发异常，用户号: {}, 错误信息: {}", cardChangeNoticeVo.getPayid(), e.getMessage(), e);
            return RestResponse.fail("换卡通知转发失败: " + e.getMessage());
        }
    }

    @Override
    @PMCTLLog(name = "存款通知转发", action = "转发")
    public RestResponse<Object> depositNotice(DepositNoticeVo depositNoticeVo) {
        log.info("接收到存款通知请求，缴费号码: {}, 目标URL: {}", depositNoticeVo.getAccount(), depositNoticeVo.getTargetUrl());
        
        try {
            // 参数验证
            if (depositNoticeVo.getTargetUrl() == null || depositNoticeVo.getTargetUrl().trim().isEmpty()) {
                log.warn("目标URL为空，无法执行转发");
                return RestResponse.fail("目标URL不能为空");
            }
            
            // 执行存款通知转发
            Object result = businessQueryService.executeDepositNotice(depositNoticeVo);
            
            log.info("存款通知转发完成，缴费号码: {}", depositNoticeVo.getAccount());
            return RestResponse.success(result);
            
        } catch (Exception e) {
            log.error("存款通知转发异常，缴费号码: {}, 错误信息: {}", depositNoticeVo.getAccount(), e.getMessage(), e);
            return RestResponse.fail("存款通知转发失败: " + e.getMessage());
        }
    }
}
