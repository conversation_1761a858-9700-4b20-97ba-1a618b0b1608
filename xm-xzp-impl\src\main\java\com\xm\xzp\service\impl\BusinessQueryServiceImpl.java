package com.xm.xzp.service.impl;

import com.xm.xzp.model.vo.BusinessQueryVo;
import com.xm.xzp.model.vo.CardChangeNoticeVo;
import com.xm.xzp.model.vo.DepositNoticeVo;
import com.xm.xzp.service.IBusinessQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 业务查询服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class BusinessQueryServiceImpl implements IBusinessQueryService {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public Object executeBusinessQuery(BusinessQueryVo businessQueryVo) {
        log.info("开始执行业务查询转发，目标URL: {}", businessQueryVo.getTargetUrl());
        
        try {
            // 构建请求参数
            Map<String, Object> requestBody = buildRequestBody(businessQueryVo);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            log.debug("转发请求参数: {}", requestBody);
            
            // 发送POST请求到目标URL
            ResponseEntity<Object> response = restTemplate.exchange(
                    businessQueryVo.getTargetUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    Object.class
            );
            
            log.info("业务查询转发成功，响应状态: {}", response.getStatusCode());
            log.debug("转发响应结果: {}", response.getBody());
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("业务查询转发失败，目标URL: {}, 错误信息: {}", businessQueryVo.getTargetUrl(), e.getMessage(), e);
            
            // 返回错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", "500");
            errorResponse.put("message", "业务查询转发失败: " + e.getMessage());
            errorResponse.put("success", false);
            
            return errorResponse;
        }
    }

    @Override
    public Object executeCardChangeNotice(CardChangeNoticeVo cardChangeNoticeVo) {
        log.info("开始执行换卡通知转发，目标URL: {}", cardChangeNoticeVo.getTargetUrl());
        
        try {
            // 构建请求参数
            Map<String, Object> requestBody = buildRequestBody(cardChangeNoticeVo);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            log.debug("转发请求参数: {}", requestBody);
            
            // 发送POST请求到目标URL
            ResponseEntity<Object> response = restTemplate.exchange(
                    cardChangeNoticeVo.getTargetUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    Object.class
            );
            
            log.info("换卡通知转发成功，响应状态: {}", response.getStatusCode());
            log.debug("转发响应结果: {}", response.getBody());
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("换卡通知转发失败，目标URL: {}, 错误信息: {}", cardChangeNoticeVo.getTargetUrl(), e.getMessage(), e);
            
            // 返回错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", "500");
            errorResponse.put("message", "换卡通知转发失败: " + e.getMessage());
            errorResponse.put("success", false);
            
            return errorResponse;
        }
    }

    @Override
    public Object executeDepositNotice(DepositNoticeVo depositNoticeVo) {
        log.info("开始执行存款通知转发，目标URL: {}", depositNoticeVo.getTargetUrl());
        
        try {
            // 构建请求参数
            Map<String, Object> requestBody = buildRequestBody(depositNoticeVo);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            log.debug("转发请求参数: {}", requestBody);
            
            // 发送POST请求到目标URL
            ResponseEntity<Object> response = restTemplate.exchange(
                    depositNoticeVo.getTargetUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    Object.class
            );
            
            log.info("存款通知转发成功，响应状态: {}", response.getStatusCode());
            log.debug("转发响应结果: {}", response.getBody());
            
            return response.getBody();
            
        } catch (Exception e) {
            log.error("存款通知转发失败，目标URL: {}, 错误信息: {}", depositNoticeVo.getTargetUrl(), e.getMessage(), e);
            
            // 返回错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", "500");
            errorResponse.put("message", "存款通知转发失败: " + e.getMessage());
            errorResponse.put("success", false);
            
            return errorResponse;
        }
    }

    /**
     * 构建转发请求的请求体（业务查询）
     */
    private Map<String, Object> buildRequestBody(BusinessQueryVo businessQueryVo) {
        Map<String, Object> requestBody = new HashMap<>();
        
        requestBody.put("busikind", businessQueryVo.getBusikind());
        requestBody.put("tradecode", businessQueryVo.getTradecode());
        requestBody.put("merchid", businessQueryVo.getMerchid());
        requestBody.put("opecd", businessQueryVo.getOpecd());
        requestBody.put("empname", businessQueryVo.getEmpname());
        requestBody.put("empcode", businessQueryVo.getEmpcode());
        requestBody.put("orgcode", businessQueryVo.getOrgcode());
        requestBody.put("orgdegree", businessQueryVo.getOrgdegree());
        requestBody.put("account", businessQueryVo.getAccount());
        requestBody.put("bgn_date", businessQueryVo.getBgn_date());
        requestBody.put("end_date", businessQueryVo.getEnd_date());
        requestBody.put("action", businessQueryVo.getAction());
        
        return requestBody;
    }

    /**
     * 构建转发请求的请求体（换卡通知）
     */
    private Map<String, Object> buildRequestBody(CardChangeNoticeVo cardChangeNoticeVo) {
        Map<String, Object> requestBody = new HashMap<>();
        
        requestBody.put("busikind", cardChangeNoticeVo.getBusikind());
        requestBody.put("tradecode", cardChangeNoticeVo.getTradecode());
        requestBody.put("merchid", cardChangeNoticeVo.getMerchid());
        requestBody.put("opecd", cardChangeNoticeVo.getOpecd());
        requestBody.put("empname", cardChangeNoticeVo.getEmpname());
        requestBody.put("empcode", cardChangeNoticeVo.getEmpcode());
        requestBody.put("orgcode", cardChangeNoticeVo.getOrgcode());
        requestBody.put("orgdegree", cardChangeNoticeVo.getOrgdegree());
        requestBody.put("payid", cardChangeNoticeVo.getPayid());
        requestBody.put("str31", cardChangeNoticeVo.getStr31());
        requestBody.put("account", cardChangeNoticeVo.getAccount());
        requestBody.put("action", cardChangeNoticeVo.getAction());
        
        return requestBody;
    }

    /**
     * 构建转发请求的请求体（存款通知）
     */
    private Map<String, Object> buildRequestBody(DepositNoticeVo depositNoticeVo) {
        Map<String, Object> requestBody = new HashMap<>();
        
        requestBody.put("busikind", depositNoticeVo.getBusikind());
        requestBody.put("tradecode", depositNoticeVo.getTradecode());
        requestBody.put("merchid", depositNoticeVo.getMerchid());
        requestBody.put("opecd", depositNoticeVo.getOpecd());
        requestBody.put("sub_ope_cd", depositNoticeVo.getSub_ope_cd());
        requestBody.put("empname", depositNoticeVo.getEmpname());
        requestBody.put("empcode", depositNoticeVo.getEmpcode());
        requestBody.put("orgcode", depositNoticeVo.getOrgcode());
        requestBody.put("orgdegree", depositNoticeVo.getOrgdegree());
        requestBody.put("account", depositNoticeVo.getAccount());
        requestBody.put("pay_id", depositNoticeVo.getPay_id());
        requestBody.put("action", depositNoticeVo.getAction());
        
        return requestBody;
    }
}
