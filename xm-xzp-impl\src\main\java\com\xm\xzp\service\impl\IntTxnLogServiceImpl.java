package com.xm.xzp.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.mapper.IntTxnLogMapper;
import com.xm.xzp.model.entity.IntTxnLog;
import com.xm.xzp.model.vo.IntTxnLogResultVo;
import com.xm.xzp.model.vo.IntTxnLogVo;
import com.xm.xzp.service.IIntTxnLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class IntTxnLogServiceImpl extends ServiceImpl<IntTxnLogMapper, IntTxnLog> 
        implements IIntTxnLogService {

    @Resource
    private IntTxnLogMapper intTxnLogMapper;

    @Override
    @DS("datasource2")
    public PageInfo<IntTxnLogResultVo> selectIntTxnLogList(IntTxnLogVo intTxnLog, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<IntTxnLogResultVo> list = intTxnLogMapper.selectIntTxnLogList(intTxnLog);
        return new PageInfo<>(list);
    }

    @Override
    @DS("datasource2")
    public PageInfo<IntTxnLog> intTxnLogList(IntTxnLogVo intTxnLog, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<IntTxnLog> list = this.list(getIntTxnLogQueryWrapper(intTxnLog));
        return new PageInfo<>(list);
    }

    private QueryWrapper<IntTxnLog> getIntTxnLogQueryWrapper(IntTxnLogVo intTxnLog) {
        QueryWrapper<IntTxnLog> queryWrapper = new QueryWrapper<>();
        if (intTxnLog != null) {
            LambdaQueryWrapper<IntTxnLog> lambdaQueryWrapper = queryWrapper.lambda();
            if (StringUtils.isNotBlank(intTxnLog.getStartTime()) && 
                StringUtils.isNotBlank(intTxnLog.getEndTime())) {
                lambdaQueryWrapper.le(IntTxnLog::getTranDt, intTxnLog.getEndTime());
                lambdaQueryWrapper.ge(IntTxnLog::getTranDt, intTxnLog.getStartTime());
            }
            if (StringUtils.isNotBlank(intTxnLog.getMerchId())) {
                lambdaQueryWrapper.eq(IntTxnLog::getMerchId, intTxnLog.getMerchId());
            }
            if (StringUtils.isNotBlank(intTxnLog.getOpeCd())) {
                lambdaQueryWrapper.eq(IntTxnLog::getOpeCd, intTxnLog.getOpeCd());
            }
            if (StringUtils.isNotBlank(intTxnLog.getPayId())) {
                lambdaQueryWrapper.eq(IntTxnLog::getPayId, intTxnLog.getPayId());
            }
            if (StringUtils.isNotBlank(intTxnLog.getUserCd()) &&
                    StringUtils.isNotBlank(intTxnLog.getSubOpeCd())) {
                lambdaQueryWrapper.eq(IntTxnLog::getPayId, intTxnLog.getSubOpeCd() + intTxnLog.getUserCd());
            }
            if (StringUtils.isNotBlank(intTxnLog.getAccCardId())){
                lambdaQueryWrapper.eq(IntTxnLog::getAccCardId, intTxnLog.getAccCardId());
            }
        }
        queryWrapper.orderByDesc("tran_dt");
        return queryWrapper;
    }

    @Override
    @DS("datasource2")
    public List<Object> queryTxnLogDetail(IntTxnLogVo intTxnLog) {
        // 不使用分页，直接返回所有数据
        return intTxnLogMapper.queryTxnLogDetail(intTxnLog);
    }

    @Override
    @DS("datasource2")
    public Workbook exportTxnLogDetail(IntTxnLogVo intTxnLog) {
        TemplateExportParams params = new TemplateExportParams("doc/xzp/缴费明细导出模板.xls");
        log.info("获取导出数据>>>>>>>>");
        List<Object> rawData = intTxnLogMapper.queryTxnLogDetail(intTxnLog);

        // 处理数据，应用与前端相同的逻辑
        List<Map<String, Object>> processedData = processExportData(rawData);

        Map<String, Object> map = new HashMap<>();
        map.put("txnLogDetailList", processedData);

        log.info("开始导出缴费明细数据，共{}条记录", processedData.size());
        return ExcelExportUtil.exportExcel(params, map);
    }

    /**
     * 处理导出数据，应用与前端相同的业务逻辑
     */
    private List<Map<String, Object>> processExportData(List<Object> rawData) {
        List<Map<String, Object>> processedList = new ArrayList<>();

        for (Object item : rawData) {
            if (item instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) item;
                Map<String, Object> processedItem = new HashMap<>(dataMap);

                // 确保str30字段存在
                if (!processedItem.containsKey("str30") || processedItem.get("str30") == null) {
                    processedItem.put("str30", "");
                }

                // 初始化应收字段
                processedItem.put("receivableNum", 0);
                processedItem.put("receivableAmt", 0);

                // 当业务代码为298020时，将num和amt的值赋值给receivableNum和receivableAmt
                String opecd = (String) processedItem.get("opecd");
                if ("298020".equals(opecd)) {
                    processedItem.put("receivableNum", processedItem.get("num"));
                    processedItem.put("receivableAmt", processedItem.get("amt"));
                    processedItem.put("num", 0);
                    processedItem.put("amt", 0);
                }

                // 计算轧差金额
                Object receivableAmtObj = processedItem.get("receivableAmt");
                Object amtObj = processedItem.get("amt");

                double receivableAmt = receivableAmtObj != null ?
                    Double.parseDouble(receivableAmtObj.toString()) / 100 : 0.0;
                double payableAmt = amtObj != null ?
                    Double.parseDouble(amtObj.toString()) / 100 : 0.0;
                double netAmt = receivableAmt - payableAmt;

                processedItem.put("netAmt", String.format("%.2f", netAmt));

                processedList.add(processedItem);
            }
        }

        return processedList;
    }
}