package com.xm.xzp.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.mapper.IntTxnLogMapper;
import com.xm.xzp.model.entity.IntTxnLog;
import com.xm.xzp.model.vo.IntTxnLogResultVo;
import com.xm.xzp.model.vo.IntTxnLogVo;
import com.xm.xzp.service.IIntTxnLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class IntTxnLogServiceImpl extends ServiceImpl<IntTxnLogMapper, IntTxnLog> 
        implements IIntTxnLogService {

    @Resource
    private IntTxnLogMapper intTxnLogMapper;

    @Override
    @DS("datasource2")
    public PageInfo<IntTxnLogResultVo> selectIntTxnLogList(IntTxnLogVo intTxnLog, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<IntTxnLogResultVo> list = intTxnLogMapper.selectIntTxnLogList(intTxnLog);
        return new PageInfo<>(list);
    }

    @Override
    @DS("datasource2")
    public PageInfo<IntTxnLog> intTxnLogList(IntTxnLogVo intTxnLog, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<IntTxnLog> list = this.list(getIntTxnLogQueryWrapper(intTxnLog));
        return new PageInfo<>(list);
    }

    private QueryWrapper<IntTxnLog> getIntTxnLogQueryWrapper(IntTxnLogVo intTxnLog) {
        QueryWrapper<IntTxnLog> queryWrapper = new QueryWrapper<>();
        if (intTxnLog != null) {
            LambdaQueryWrapper<IntTxnLog> lambdaQueryWrapper = queryWrapper.lambda();
            if (StringUtils.isNotBlank(intTxnLog.getStartTime()) && 
                StringUtils.isNotBlank(intTxnLog.getEndTime())) {
                lambdaQueryWrapper.le(IntTxnLog::getTranDt, intTxnLog.getEndTime());
                lambdaQueryWrapper.ge(IntTxnLog::getTranDt, intTxnLog.getStartTime());
            }
            if (StringUtils.isNotBlank(intTxnLog.getMerchId())) {
                lambdaQueryWrapper.eq(IntTxnLog::getMerchId, intTxnLog.getMerchId());
            }
            if (StringUtils.isNotBlank(intTxnLog.getOpeCd())) {
                lambdaQueryWrapper.eq(IntTxnLog::getOpeCd, intTxnLog.getOpeCd());
            }
            if (StringUtils.isNotBlank(intTxnLog.getPayId())) {
                lambdaQueryWrapper.eq(IntTxnLog::getPayId, intTxnLog.getPayId());
            }
            if (StringUtils.isNotBlank(intTxnLog.getUserCd()) &&
                    StringUtils.isNotBlank(intTxnLog.getSubOpeCd())) {
                lambdaQueryWrapper.eq(IntTxnLog::getPayId, intTxnLog.getSubOpeCd() + intTxnLog.getUserCd());
            }
            if (StringUtils.isNotBlank(intTxnLog.getAccCardId())){
                lambdaQueryWrapper.eq(IntTxnLog::getAccCardId, intTxnLog.getAccCardId());
            }
        }
        queryWrapper.orderByDesc("tran_dt");
        return queryWrapper;
    }

    @Override
    @DS("datasource2")
    public List<Object> queryTxnLogDetail(IntTxnLogVo intTxnLog) {
        // 不使用分页，直接返回所有数据
        return intTxnLogMapper.queryTxnLogDetail(intTxnLog);
    }

    @Override
    @DS("datasource2")
    public Workbook exportTxnLogDetail(IntTxnLogVo intTxnLog) {
        TemplateExportParams params = new TemplateExportParams("doc/xzp/缴费明细导出模板.xls");
        log.info("获取导出数据>>>>>>>>");
        List<Object> txnLogDetailList = intTxnLogMapper.queryTxnLogDetail(intTxnLog);
        Map<String, Object> map = new HashMap<>(txnLogDetailList.size());
        map.put("txnLogDetailList", txnLogDetailList);

        log.info("开始导出缴费明细数据，共{}条记录", txnLogDetailList.size());
        return ExcelExportUtil.exportExcel(params, map);
    }
}